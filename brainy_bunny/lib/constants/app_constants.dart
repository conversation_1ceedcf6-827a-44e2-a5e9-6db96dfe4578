// lib/constants/app_constants.dart
import 'dart:io';

/// Class to hold all application constants
class AppConstants {
  // Firebase database keys
  static const String FULL_GAME_PURCHASED_KEY = 'full_game_purchased';
  static const String PURCHASE_ID_KEY = 'purchase_id';

  // In-app purchase configuration
  static const String IOS_PRODUCT_ID = 'full_game_unlock_apple';
  static const String ANDROID_PRODUCT_ID = 'full_game_unlock';
  static const Set<String> IAP_PRODUCT_IDS = {'full_game_unlock_apple', 'full_game_unlock'};

  /// Get platform-specific Product ID
  static String get PRODUCT_ID {
    if (Platform.isIOS) {
      return IOS_PRODUCT_ID;
    } else {
      return ANDROID_PRODUCT_ID;
    }
  }
  static const String FALLBACK_PRICE = '\$6.99';
  static const String APP_SALT = 'brainy_bunny_secure_salt_2024';

  // Asset paths
  static const String SETTINGS_ICON_PATH = 'icon_gear.png';
  static const String LOCK_ICON_PATH = 'icon_padlock.png';
  static const String HOME_ICON_PATH = 'icon_home.png';
  static const String HOME_BACKGROUND_PATH = 'home_background.jpg';

  // Audio paths
  static const List<String> AUDIO_FILES = [
    'button_press.mp3',
    'match_sound.wav',
    'round_complete_sound.wav',
    'game_complete_sound.wav',
    'Menu.mp3', // menu music
    'Background_1.mp3', // Game music option 1
    'Background_2.mp3', // Game music option 2
    'Background_3.mp3', // Game music option 3
  ];

  // Game configuration
  static const int FREE_GAMES_COUNT = 5;
  static const int TOTAL_GAMES_COUNT = 15;
  static const List<int> ROUND_PAIRS = [1, 2, 3, 4, 5];

  // Game paths pattern
  static String getGameIconPath(int index) => 'game_${index + 1}/icon.png';

  static String getGamePath(int index) => 'game_${index + 1}';

  // Overlay identifiers
  static const String LOADING_OVERLAY = 'loading';
  static const String PENDING_PURCHASE_OVERLAY = 'pendingPurchase';
  static const String PURCHASE_ERROR_OVERLAY = 'purchaseError';
  static const String INVALID_PURCHASE_OVERLAY = 'invalidPurchase';

  // Purchase flow overlays
  static const String PURCHASE_OFFER_OVERLAY = 'purchaseOffer';
  static const String AGE_VERIFICATION_OVERLAY = 'ageVerification';

  // ADDED: Debug overlay
  static const String DEBUG_OVERLAY = 'debug';
}