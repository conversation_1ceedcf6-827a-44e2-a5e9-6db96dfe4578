// lib/ui/overlays/iap_debug_overlay.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Debug overlay to show IAP status and troubleshooting info
class IAPDebugOverlay extends StatefulWidget {
  const IAPDebugOverlay({super.key});

  @override
  State<IAPDebugOverlay> createState() => _IAPDebugOverlayState();
}

class _IAPDebugOverlayState extends State<IAPDebugOverlay> {
  final PurchaseManager _purchaseManager = PurchaseManager();
  String _debugInfo = 'Loading...';

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    final buffer = StringBuffer();
    
    buffer.writeln('=== IAP DEBUG INFO ===');
    buffer.writeln('Platform: ${Platform.operatingSystem}');
    buffer.writeln('Is Debug: $kDebugMode');
    buffer.writeln('Is Release: $kReleaseMode');
    buffer.writeln('Product ID: ${AppConstants.PRODUCT_ID}');
    buffer.writeln('iOS Product ID: ${AppConstants.IOS_PRODUCT_ID}');
    buffer.writeln('Android Product ID: ${AppConstants.ANDROID_PRODUCT_ID}');
    buffer.writeln('');
    
    buffer.writeln('=== PURCHASE MANAGER STATE ===');
    buffer.writeln('Initialized: ${_purchaseManager.isInitialized}');
    buffer.writeln('Purchased: ${_purchaseManager.isPurchased}');
    buffer.writeln('Price Loaded: ${_purchaseManager.priceLoaded}');
    buffer.writeln('Display Price: ${_purchaseManager.displayPrice}');
    buffer.writeln('Using Fallback Price: ${_purchaseManager.isUsingFallbackPrice}');
    buffer.writeln('Price Load Error: ${_purchaseManager.priceLoadError ?? 'None'}');
    buffer.writeln('Product Details: ${_purchaseManager.productDetails != null ? 'Available' : 'Not Available'}');
    
    if (_purchaseManager.productDetails != null) {
      final details = _purchaseManager.productDetails!;
      buffer.writeln('  - ID: ${details.id}');
      buffer.writeln('  - Title: ${details.title}');
      buffer.writeln('  - Price: ${details.price}');
      buffer.writeln('  - Currency: ${details.currencyCode}');
    }
    
    buffer.writeln('');
    buffer.writeln('=== TESTFLIGHT CHECKLIST ===');
    buffer.writeln('✓ Check App Store Connect:');
    buffer.writeln('  - Product ID configured?');
    buffer.writeln('  - Product status: Ready to Submit?');
    buffer.writeln('  - Pricing set for all regions?');
    buffer.writeln('  - IAP LINKED TO APP VERSION? ← MAIN ISSUE');
    buffer.writeln('✓ Check TestFlight:');
    buffer.writeln('  - Using TestFlight sandbox account?');
    buffer.writeln('  - Signed out of production App Store?');
    buffer.writeln('  - App version matches store config?');
    buffer.writeln('  - Bundle ID matches exactly?');
    
    setState(() {
      _debugInfo = buffer.toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.8),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'IAP Debug Info',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          setState(() {
                            _debugInfo = 'Refreshing...';
                          });
                          await _purchaseManager.refreshPrice(forceRefresh: true);
                          await _loadDebugInfo();
                        },
                        child: const Text('Refresh'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _debugInfo,
                      style: const TextStyle(
                        color: Colors.green,
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          await _purchaseManager.purchaseFullGame();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Purchase initiated')),
                          );
                        } catch (e) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Purchase error: $e')),
                          );
                        }
                      },
                      child: const Text('Test Purchase'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                       onPressed: () async {
                        try {
                          await _purchaseManager.restorePurchases();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Restore initiated')),
                          );
                        } catch (e) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Restore error: $e')),
                          );
                        }
                      },
                      child: const Text('Test Restore'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
