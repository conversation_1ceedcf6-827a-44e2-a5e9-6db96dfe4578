// lib/ui/overlays/purchase_flow_overlays.dart
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flame/game.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

/// Beautiful purchase offer overlay with real price display
class PurchaseOfferOverlay extends StatefulWidget {
  final GameWidget gameWidget;

  const PurchaseOfferOverlay({
    super.key,
    required this.gameWidget,
  });

  @override
  State<PurchaseOfferOverlay> createState() => _PurchaseOfferOverlayState();
}

class _PurchaseOfferOverlayState extends State<PurchaseOfferOverlay>
    with TickerProviderStateMixin {

  late AnimationController _backgroundController;
  late AnimationController _contentController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  // ADDED: Price management
  final PurchaseManager _purchaseManager = PurchaseManager();
  String _displayPrice = AppConstants.FALLBACK_PRICE;
  bool _priceLoading = true;

  @override
  void initState() {
    super.initState();

    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _contentController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeOutBack,
    ));

    _backgroundController.forward();
    Future.delayed(const Duration(milliseconds: 100), () {
      _contentController.forward();
    });

    // ADDED: Initialize and load price
    _initializePriceDisplay();
  }

  /// ADDED: Initialize price display
  Future<void> _initializePriceDisplay() async {
    try {
      // Ensure purchase manager is initialized
      if (!_purchaseManager.isInitialized) {
        await _purchaseManager.initialize();
      }

      if (mounted) {
        setState(() {
          _displayPrice = _purchaseManager.displayPrice;
          _priceLoading = !_purchaseManager.priceLoaded;
        });

        if (kDebugMode) {
          print('💰 Price display initialized: $_displayPrice (loading: $_priceLoading)');
        }

        // Listen for price updates
        _purchaseManager.priceStream.listen((newPrice) {
          if (mounted) {
            setState(() {
              _displayPrice = newPrice;
              _priceLoading = false;
            });
            if (kDebugMode) {
              print('💰 Price updated: $newPrice');
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing price display: $e');
      }
      if (mounted) {
        setState(() {
          _displayPrice = AppConstants.FALLBACK_PRICE;
          _priceLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 700;
    final isTablet = screenSize.width > 600;

    return AnimatedBuilder(
      animation: Listenable.merge([_backgroundController, _contentController]),
      builder: (context, child) {
        return Material(
          color: Colors.black.withValues(alpha: 0.7 * _backgroundAnimation.value),
          child: Center(
            child: SlideTransition(
              position: _slideAnimation,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: screenSize.width * 0.95,
                  height: screenSize.height * 0.95,
                  margin: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF87CEEB), // Sky blue
                        Color(0xFF4A90E2), // Deeper blue
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Header
                      Padding(
                        padding: EdgeInsets.all(isTablet ? 12 : (isSmallScreen ? 6 : 8)),
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: _closeOverlay,
                              child: Container(
                                width: isTablet ? 36 : 28,
                                height: isTablet ? 36 : 28,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: isTablet ? 20 : 16,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  Text(
                                    'Unlock',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: isTablet ? 24 : (isSmallScreen ? 16 : 20),
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                  Text(
                                    '${AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT} more games',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: isTablet ? 18 : (isSmallScreen ? 12 : 16),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Game icons grid - takes most of the available space
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: isTablet ? 24 : (isSmallScreen ? 12 : 16)),
                          child: Center(
                            child: _buildGameIconsGrid(isSmallScreen),
                          ),
                        ),
                      ),

                      // Bottom section - ALL elements in same row, centered
                      Container(
                        padding: EdgeInsets.all(isTablet ? 20 : (isSmallScreen ? 12 : 16)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Feature 1
                            _buildFeatureItem(
                              Icons.favorite,
                              'One-time\npurchase',
                              Colors.orange,
                              isSmallScreen,
                              isTablet,
                            ),

                            SizedBox(width: isTablet ? 30 : (isSmallScreen ? 20 : 25)),

                            // Feature 2
                            _buildFeatureItem(
                              Icons.check_circle,
                              'Unlock all\ngames',
                              Colors.green,
                              isSmallScreen,
                              isTablet,
                            ),

                            SizedBox(width: isTablet ? 30 : (isSmallScreen ? 20 : 25)),

                            // ENHANCED: Purchase button with real price and loading state
                            _buildPurchaseButton(isSmallScreen, isTablet, screenSize),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// ADDED: Build purchase button with real price display
  Widget _buildPurchaseButton(bool isSmallScreen, bool isTablet, Size screenSize) {
    return GestureDetector(
      onTap: _priceLoading ? null : _handlePurchasePressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: _getResponsiveButtonWidth(screenSize, isSmallScreen, isTablet),
        height: _getResponsiveButtonHeight(screenSize, isSmallScreen, isTablet),
        decoration: BoxDecoration(
          gradient: _priceLoading
              ? LinearGradient(
            colors: [
              Colors.grey.shade400,
              Colors.grey.shade600,
            ],
          )
              : LinearGradient(
            colors: [
              Colors.orange.shade400,
              Colors.orange.shade600,
            ],
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: (_priceLoading ? Colors.grey : Colors.orange).withOpacity(0.4),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: _priceLoading
              ? SizedBox(
            width: _getResponsiveButtonFontSize(screenSize, isSmallScreen, isTablet) * 0.8,
            height: _getResponsiveButtonFontSize(screenSize, isSmallScreen, isTablet) * 0.8,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
              : FittedBox(
            fit: BoxFit.scaleDown,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _displayPrice,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: _getResponsiveButtonFontSize(screenSize, isSmallScreen, isTablet),
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_displayPrice != AppConstants.FALLBACK_PRICE && !isSmallScreen) ...[
                  SizedBox(height: 2),
                  Text(
                    'Buy Now',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: (_getResponsiveButtonFontSize(screenSize, isSmallScreen, isTablet) * 0.4).clamp(8.0, 12.0),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Calculate responsive button width
  double _getResponsiveButtonWidth(Size screenSize, bool isSmallScreen, bool isTablet) {
    if (isTablet) {
      return screenSize.width * 0.18;
    } else if (isSmallScreen) {
      return screenSize.width * 0.25;
    } else {
      return screenSize.width * 0.22;
    }
  }

  /// Calculate responsive button height
  double _getResponsiveButtonHeight(Size screenSize, bool isSmallScreen, bool isTablet) {
    if (isTablet) {
      return screenSize.height * 0.08;
    } else if (isSmallScreen) {
      return screenSize.height * 0.06;
    } else {
      return screenSize.height * 0.07;
    }
  }

  /// Calculate responsive button font size
  double _getResponsiveButtonFontSize(Size screenSize, bool isSmallScreen, bool isTablet) {
    if (isTablet) {
      return 28;
    } else if (isSmallScreen) {
      return 18;
    } else {
      return 22;
    }
  }

  Widget _buildGameIconsGrid([bool isSmallScreen = false]) {
    const startIndex = AppConstants.FREE_GAMES_COUNT;
    const endIndex = AppConstants.TOTAL_GAMES_COUNT;
    final lockedGameCount = endIndex - startIndex;

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;

        double spacing;
        double borderRadius;

        if (screenWidth > 1000) {
          spacing = 8.0;
          borderRadius = 6.0;
        } else if (screenWidth > 600) {
          spacing = 6.0;
          borderRadius = 5.0;
        } else if (screenHeight < 700) {
          spacing = 4.0;
          borderRadius = 3.0;
        } else {
          spacing = 5.0;
          borderRadius = 4.0;
        }

        final availableWidth = constraints.maxWidth - (spacing * 4);
        final availableHeight = constraints.maxHeight - spacing;

        final maxItemWidth = availableWidth / 5;
        final maxItemHeight = availableHeight / 2;

        var itemSize = (maxItemWidth < maxItemHeight ? maxItemWidth : maxItemHeight);
        itemSize = itemSize.clamp(60.0, 150.0);

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            childAspectRatio: 1.0,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            mainAxisExtent: itemSize,
          ),
          itemCount: lockedGameCount,
          itemBuilder: (context, gridIndex) {
            final gameIndex = startIndex + gridIndex;

            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(borderRadius),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: screenWidth > 600 ? 1.0 : 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: screenWidth > 600 ? 2 : 1,
                    offset: const Offset(0, 0.5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(borderRadius),
                child: Container(
                  padding: const EdgeInsets.all(3),
                  child: _getGameIcon(gameIndex),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _getGameIcon(int gameIndex) {
    final iconPath = 'assets/images/${AppConstants.getGameIconPath(gameIndex)}';
    final screenWidth = MediaQuery.of(context).size.width;

    if (kDebugMode) {
      print('Loading game icon for index $gameIndex: $iconPath');
    }

    return Image.asset(
      iconPath,
      fit: BoxFit.contain,
      cacheWidth: 150,
      cacheHeight: 150,
      errorBuilder: (context, error, stackTrace) {
        if (kDebugMode) {
          print('Failed to load icon: $iconPath');
          print('Error: $error');
        }

        final fallbackIconSize = screenWidth > 600 ? 28.0 : 20.0;
        final textSize = screenWidth > 600 ? 10.0 : 8.0;

        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.purple.shade100,
                Colors.blue.shade100,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.extension,
                  color: Colors.purple.shade400,
                  size: fallbackIconSize,
                ),
                const SizedBox(height: 2),
                Text(
                  'Game\n${gameIndex + 1}',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.purple.shade600,
                    fontSize: textSize,
                    fontWeight: FontWeight.w600,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureItem(IconData icon, String text, Color color, [bool isSmallScreen = false, bool isTablet = false]) {
    final circleSize = isTablet ? 40.0 : (isSmallScreen ? 30.0 : 35.0);
    final iconSize = isTablet ? 20.0 : (isSmallScreen ? 15.0 : 18.0);
    final fontSize = isTablet ? 12.0 : (isSmallScreen ? 9.0 : 10.0);
    final spacing = isTablet ? 10.0 : (isSmallScreen ? 6.0 : 8.0);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Round icon
        Container(
          width: circleSize,
          height: circleSize,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: iconSize,
          ),
        ),
        SizedBox(width: spacing),
        // Text to the right of icon
        Text(
          text,
          style: TextStyle(
            color: Colors.white,
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
            height: 1.2,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.3),
                offset: const Offset(0, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handlePurchasePressed() {
    if (_priceLoading) return;

    _closeOverlay();

    Future.delayed(const Duration(milliseconds: 300), () {
      final game = widget.gameWidget.game;
      if (game != null) {
        game.overlays.add(AppConstants.AGE_VERIFICATION_OVERLAY);
      }
    });
  }

  void _closeOverlay() {
    final game = widget.gameWidget.game;
    if (game != null && game.overlays.isActive(AppConstants.PURCHASE_OFFER_OVERLAY)) {
      game.overlays.remove(AppConstants.PURCHASE_OFFER_OVERLAY);
    }
  }
}

/// Age verification overlay - enhanced with "already owned" handling
class AgeVerificationOverlay extends StatefulWidget {
  final GameWidget gameWidget;

  const AgeVerificationOverlay({
    super.key,
    required this.gameWidget,
  });

  @override
  State<AgeVerificationOverlay> createState() => _AgeVerificationOverlayState();
}

class _AgeVerificationOverlayState extends State<AgeVerificationOverlay>
    with TickerProviderStateMixin {

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _backgroundAnimation;

  String _userAnswer = '';
  late int _num1;
  late int _num2;
  late int _correctAnswer;
  bool _showError = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _generateMathProblem();
    _animationController.forward();
  }

  void _generateMathProblem() {
    final random = Random();
    _num1 = 10 + random.nextInt(20);
    _num2 = 10 + random.nextInt(20);
    _correctAnswer = _num1 + _num2;
    if (kDebugMode) {
      print('Generated math problem: $_num1 + $_num2 = $_correctAnswer');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.black.withValues(alpha: 0.8 * _backgroundAnimation.value),
          child: Center(
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: screenSize.width * 0.95,
                height: screenSize.height * 0.95,
                margin: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF87CEEB),
                      Color(0xFF4A90E2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24),
                  child: Column(
                    children: [
                      // Header
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: _closeOverlay,
                              child: Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  'Age verification',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: isSmallScreen ? 16 : 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 36),
                          ],
                        ),
                      ),

                      // Main content area
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          child: isSmallScreen
                              ? _buildCompactLayout()
                              : _buildHorizontalLayout(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHorizontalLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Answer panel on the LEFT
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$_num1 + $_num2 =',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        offset: Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  width: 140,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Text(
                    _userAnswer.isEmpty ? '?' : _userAnswer,
                    style: TextStyle(
                      color: _userAnswer.isEmpty ? Colors.grey.shade500 : Colors.black87,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                if (_showError) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Try again',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),

        // Keypad on the RIGHT
        Expanded(
          flex: 3,
          child: _buildKeypad(),
        ),
      ],
    );
  }

  Widget _buildCompactLayout() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '$_num1 + $_num2 =',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 80,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _userAnswer.isEmpty ? '?' : _userAnswer,
                      style: TextStyle(
                        color: _userAnswer.isEmpty ? Colors.grey : Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              if (_showError) ...[
                const SizedBox(height: 6),
                const Text(
                  'Try again',
                  style: TextStyle(
                    color: Colors.redAccent,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 12),
        _buildKeypad(),
      ],
    );
  }

  Widget _buildKeypad() {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    final maxAvailableHeight = screenHeight * 0.5;
    final maxAvailableWidth = screenWidth * 0.5;

    final buttonSizeFromHeight = (maxAvailableHeight / 5.5);
    final buttonSizeFromWidth = (maxAvailableWidth / 4.0);

    final buttonSize = (buttonSizeFromHeight < buttonSizeFromWidth ? buttonSizeFromHeight : buttonSizeFromWidth)
        .clamp(50.0, 90.0);

    final spacing = buttonSize * 0.18;
    final totalHeight = (buttonSize * 4) + (spacing * 3);

    return Container(
      width: maxAvailableWidth,
      height: totalHeight,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          mainAxisExtent: buttonSize,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          return _buildKeypadButton(index, buttonSize);
        },
      ),
    );
  }

  Widget _buildKeypadButton(int index, double buttonSize) {
    Widget content;
    VoidCallback? onTap;
    Color iconColor = Colors.white;

    final fontSize = (buttonSize * 0.4).clamp(20.0, 36.0);
    final iconSize = (buttonSize * 0.35).clamp(20.0, 32.0);

    if (index >= 0 && index <= 8) {
      final number = index + 1;
      content = Text(
        '$number',
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ),
      );
      onTap = () => _addNumber('$number');
    } else if (index == 9) {
      iconColor = Colors.yellow;
      content = Icon(
        Icons.backspace_outlined,
        color: iconColor,
        size: iconSize,
      );
      onTap = _removeLastNumber;
    } else if (index == 10) {
      content = Text(
        '0',
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ),
      );
      onTap = () => _addNumber('0');
    } else {
      iconColor = const Color(0xFF00C851);
      content = Icon(
        Icons.check_circle,
        color: iconColor,
        size: iconSize,
      );
      onTap = _checkAnswer;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        splashColor: Colors.white.withOpacity(0.3),
        highlightColor: Colors.white.withOpacity(0.1),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.25),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(child: content),
        ),
      ),
    );
  }

  void _addNumber(String number) {
    if (_userAnswer.length < 3) {
      setState(() {
        _userAnswer += number;
        _showError = false;
      });
      if (kDebugMode) {
        print('Added number: $number, current answer: $_userAnswer');
      }
    }
  }

  void _removeLastNumber() {
    if (_userAnswer.isNotEmpty) {
      setState(() {
        _userAnswer = _userAnswer.substring(0, _userAnswer.length - 1);
        _showError = false;
      });
      if (kDebugMode) {
        print('Removed number, current answer: $_userAnswer');
      }
    }
  }

  void _checkAnswer() {
    if (_userAnswer.isEmpty) return;

    final userNum = int.tryParse(_userAnswer);
    if (kDebugMode) {
      print('Checking answer: $_userAnswer = $userNum, correct: $_correctAnswer');
    }

    if (userNum == _correctAnswer) {
      if (kDebugMode) {
        print('Answer correct! Proceeding with purchase.');
      }
      _closeOverlay();
      _processPurchase();
    } else {
      if (kDebugMode) {
        print('Answer incorrect. Clearing and showing error.');
      }
      setState(() {
        _showError = true;
        _userAnswer = '';
      });
    }
  }

  void _processPurchase() async {
    final game = widget.gameWidget.game;
    if (game != null) {
      try {
        final purchaseManager = PurchaseManager();

        if (kDebugMode) {
          print('🚀 Starting purchase process...');
        }

        await purchaseManager.purchaseFullGame();

        if (kDebugMode) {
          print('✅ Purchase process started - PurchaseManager will handle overlays');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error starting purchase: $e');
        }
        game.overlays.add(AppConstants.PURCHASE_ERROR_OVERLAY);
      }
    }
  }

  void _closeOverlay() {
    final game = widget.gameWidget.game;
    if (game != null && game.overlays.isActive(AppConstants.AGE_VERIFICATION_OVERLAY)) {
      game.overlays.remove(AppConstants.AGE_VERIFICATION_OVERLAY);
    }
  }
}