import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';
import 'package:brainy_bunny/ui/overlays/loading_overlay.dart';

class GameScreen extends StatefulWidget {
  final String gameName;

  const GameScreen({
    super.key,
    required this.gameName,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with WidgetsBindingObserver {
  late DragGame _game;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _game = DragGame(gameName: widget.gameName);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive:
        // Pause the game when going to background
        _game.pauseEngine();
        break;
      case AppLifecycleState.resumed:
        // Resume the game when coming back
        _game.resumeEngine();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop();
        return false;
      },
      child: Scaffold(
        body: GameWidget<DragGame>(
          game: _game,
          loadingBuilder: (_) => const LoadingOverlay(),
          overlayBuilderMap: {
            AppConstants.LOADING_OVERLAY: (context, game) =>
                const LoadingOverlay(),
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}