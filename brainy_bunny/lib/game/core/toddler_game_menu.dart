// lib/game/core/toddler_game_menu.dart
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/components/menu_grid.dart';
import 'package:brainy_bunny/game/components/confetti_component.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';
import 'package:brainy_bunny/services/audio_service.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

class GameHolder extends Component {
  DragGame? game;

  @override
  void onRemove() {
    if (game != null) {
      game = null;
    }
    super.onRemove();
  }
}

class ToddlerGameMenu extends FlameGame with TapCallbacks {
  late MenuGrid menuGrid;
  GameHolder gameHolder = GameHolder();
  bool isGameTransitioning = false;
  int? pendingGameIndex;

  // Purchase manager instance
  late PurchaseManager _purchaseManager;

  // ADDED: Track if we've already applied purchase success
  bool _purchaseSuccessApplied = false;

  // ADDED: Track if menu is currently visible
  bool _menuVisible = true;

  // ADDED: Confetti component for unlock animation
  FlameConfetti? _confettiComponent;

  // ADDED: Public getter for menuGrid access
  MenuGrid get menuGridComponent => menuGrid;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Initialize purchase manager
    _purchaseManager = PurchaseManager();
    if (!_purchaseManager.isInitialized) {
      await _purchaseManager.initialize();
    }

    if (kDebugMode) {
      print('🎮 ToddlerGameMenu - PurchaseManager state:');
      print('   - isInitialized: ${_purchaseManager.isInitialized}');
      print('   - isPurchased: ${_purchaseManager.isPurchased}');

      if (_purchaseManager.isPurchased) {
        print('🔓 All games will be unlocked');
        _purchaseSuccessApplied = true; // Mark as already applied
      } else {
        print('🔒 Premium games (${AppConstants.FREE_GAMES_COUNT}-${AppConstants.TOTAL_GAMES_COUNT - 1}) will be locked');
      }
    }

    // Load background
    final background = await loadSprite(AppConstants.HOME_BACKGROUND_PATH);
    add(SpriteComponent(sprite: background, size: size));

    // ADDED: Initialize confetti component (but don't add it yet)
    _confettiComponent = FlameConfetti(
      maxParticles: 150, // More particles for celebration
      autoPlay: false, // We'll trigger it manually
    );

    // Create initial menu grid
    await _createInitialMenuGrid();

    add(gameHolder);

    // Listen to purchase status changes
    _purchaseManager.purchaseStream.listen((isPurchased) {
      if (kDebugMode) {
        print('📡 Purchase stream update: isPurchased=$isPurchased, already applied=$_purchaseSuccessApplied');
      }
      if (isPurchased && !_purchaseSuccessApplied) {
        onPurchaseSuccessWithAnimation();
      }
    });

    // ADDED: Start menu music after everything is loaded and ready
    if (kDebugMode) {
      print('🎵 ToddlerGameMenu fully loaded - starting menu music');
    }

    // Delay to ensure the game is completely ready
    Future.delayed(const Duration(milliseconds: 200), () {
      AudioService.instance.forcePlayMenuMusic();

      if (kDebugMode) {
        print('✅ Menu music started in ToddlerGameMenu onLoad');
      }
    });
  }

  /// ADDED: Create initial menu grid
  Future<void> _createInitialMenuGrid() async {
    final gameIcons = List.generate(
      AppConstants.TOTAL_GAMES_COUNT,
          (index) => AppConstants.getGameIconPath(index),
    );

    final gameCallbacks = List.generate(
      AppConstants.TOTAL_GAMES_COUNT,
          (index) => () => _onGameButtonTap(index),
    );

    // CRITICAL: Use current purchase status for menu grid
    menuGrid = MenuGrid(
      buttonAssets: gameIcons,
      buttonCallbacks: gameCallbacks,
      isFullGamePurchased: _purchaseManager.isPurchased,
    );

    if (kDebugMode) {
      print('🎯 MenuGrid created with isFullGamePurchased: ${_purchaseManager.isPurchased}');
    }
    add(menuGrid);
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (pendingGameIndex != null && !isGameTransitioning) {
      final index = pendingGameIndex!;
      pendingGameIndex = null;
      _launchGame(index);
    }
  }

  void _onGameButtonTap(int index) {
    if (kDebugMode) {
      print('🎮 Game button tapped: index $index, isTransitioning: $isGameTransitioning');
    }

    if (isGameTransitioning) {
      return;
    }

    // Use purchase manager to check if game is unlocked
    final isUnlocked = _purchaseManager.isGameUnlocked(index);

    if (kDebugMode) {
      print('🔍 Game $index unlock check:');
      print('   - Index: $index');
      print('   - Free games count: ${AppConstants.FREE_GAMES_COUNT}');
      print('   - Is free game: ${index < AppConstants.FREE_GAMES_COUNT}');
      print('   - Purchase manager says unlocked: $isUnlocked');
      print('   - Purchase manager is purchased: ${_purchaseManager.isPurchased}');
    }

    if (isUnlocked) {
      AudioService.instance.playSound('button_press.mp3');
      pendingGameIndex = index;
      overlays.add(AppConstants.LOADING_OVERLAY);
      if (kDebugMode) {
        print('▶️ Starting game $index');
      }
    } else {
      // Show purchase offer overlay
      if (kDebugMode) {
        print('🔒 Game $index is locked - showing purchase offer');
      }
      _showPurchaseOffer();
    }
  }

  // Show beautiful purchase offer overlay
  void _showPurchaseOffer() {
    AudioService.instance.playSound('button_press.mp3');

    _clearPurchaseOverlays();

    overlays.add(AppConstants.PURCHASE_OFFER_OVERLAY);

    if (kDebugMode) {
      print('🛒 Showing purchase offer overlay');
    }
  }

  // Clear purchase overlays
  void _clearPurchaseOverlays() {
    final purchaseOverlays = [
      AppConstants.PURCHASE_OFFER_OVERLAY,
      AppConstants.AGE_VERIFICATION_OVERLAY,
      AppConstants.PENDING_PURCHASE_OVERLAY,
      AppConstants.PURCHASE_ERROR_OVERLAY,
      AppConstants.INVALID_PURCHASE_OVERLAY,
    ];

    for (final overlay in purchaseOverlays) {
      if (overlays.isActive(overlay)) {
        overlays.remove(overlay);
        if (kDebugMode) {
          print('🧹 Removed overlay from menu: $overlay');
        }
      }
    }
  }

  void _launchGame(int index) {
    if (isGameTransitioning) {
      return;
    }

    isGameTransitioning = true;

    // CRITICAL: Hide menu before showing game
    if (_menuVisible && children.contains(menuGrid)) {
      remove(menuGrid);
      _menuVisible = false;
      if (kDebugMode) {
        print('🎮 Menu hidden for game launch');
      }
    }

    if (gameHolder.game != null) {
      try {
        final oldGame = gameHolder.game!;
        gameHolder.game = null;
        gameHolder.remove(oldGame);
      } catch (e) {
        if (kDebugMode) {
          print('Error removing existing game: $e');
        }
      }
    }

    Future.delayed(const Duration(milliseconds: 50), () {
      Future.delayed(const Duration(milliseconds: 50), () {
        try {
          if (overlays.isActive(AppConstants.LOADING_OVERLAY)) {
            overlays.remove(AppConstants.LOADING_OVERLAY);
          }

          final newGame = DragGame(
            gameName: AppConstants.getGamePath(index),
            onReturnToMenu: returnToMenu,
          );
          gameHolder.game = newGame;
          gameHolder.add(newGame);

          if (kDebugMode) {
            print('✅ New game added successfully');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error adding new game: $e');
          }
          // Show menu again if game failed to load
          if (!_menuVisible && !children.contains(menuGrid)) {
            add(menuGrid);
            _menuVisible = true;
          }
        } finally {
          isGameTransitioning = false;
        }
      });
    });
  }

  void returnToMenu() {
    if (kDebugMode) {
      print('🏠 Returning to menu from game');
      print('   isGameTransitioning: $isGameTransitioning');
      print('   gameHolder.game: ${gameHolder.game != null}');
      print('   menuVisible: $_menuVisible');
    }

    // Allow return even if transitioning
    if (gameHolder.game != null) {
      isGameTransitioning = true;

      try {
        overlays.add(AppConstants.LOADING_OVERLAY);

        final game = gameHolder.game!;
        gameHolder.game = null;
        gameHolder.remove(game);

        // CRITICAL: Show menu again when returning
        if (!_menuVisible && !children.contains(menuGrid)) {
          add(menuGrid);
          _menuVisible = true;
          if (kDebugMode) {
            print('🏠 Menu shown again after returning');
          }
        }

        // CRITICAL: Force switch to menu music when returning from game
        if (kDebugMode) {
          print('🎵 Switching from game music to menu music');
        }

        // Use force play to ensure menu music starts
        AudioService.instance.forcePlayMenuMusic();

        Future.delayed(const Duration(milliseconds: 200), () {
          if (overlays.isActive(AppConstants.LOADING_OVERLAY)) {
            overlays.remove(AppConstants.LOADING_OVERLAY);
          }

          // ADDED: Extra ensure menu music is playing after loading overlay removed
          Future.delayed(const Duration(milliseconds: 300), () {
            if (kDebugMode) {
              print('🎵 Double-checking menu music after return');
            }
            AudioService.instance.forcePlayMenuMusic();
          });
        });
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error returning to menu: $e');
        }

        if (overlays.isActive(AppConstants.LOADING_OVERLAY)) {
          overlays.remove(AppConstants.LOADING_OVERLAY);
        }

        // Make sure menu is visible on error
        if (!_menuVisible && !children.contains(menuGrid)) {
          add(menuGrid);
          _menuVisible = true;
        }
      } finally {
        isGameTransitioning = false;
      }
    } else {
      // If no game is active, just ensure menu is visible
      if (!_menuVisible && !children.contains(menuGrid)) {
        add(menuGrid);
        _menuVisible = true;
        if (kDebugMode) {
          print('🏠 Menu restored (no active game)');
        }
      }
    }
  }

  /// LEGACY: Simple purchase success for compatibility
  void onPurchaseSuccess() {
    onPurchaseSuccessWithAnimation();
  }

  /// BEAUTIFUL: Purchase success with amazing unlock animation and confetti
  void onPurchaseSuccessWithAnimation() {
    if (kDebugMode) {
      print('🎉 Purchase successful in ToddlerGameMenu - starting unlock animation');
    }

    // Check if already applied to prevent multiple calls
    if (_purchaseSuccessApplied) {
      if (kDebugMode) {
        print('⚠️ Purchase success already applied - skipping');
      }
      return;
    }

    try {
      // Mark as applied to prevent duplicate calls
      _purchaseSuccessApplied = true;

      // First, clear any purchase overlays
      _clearPurchaseOverlays();

      // Start confetti celebration
      _startConfettiCelebration();

      // Play success sound
      AudioService.instance.playSound('game_complete_sound.wav');

      // Animate unlock with a delay to let confetti start
      Future.delayed(const Duration(milliseconds: 500), () {
        _animateUnlockGames();
      });

      if (kDebugMode) {
        print('✅ Unlock animation and confetti started');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in unlock animation: $e');
      }
      // Fallback - just unlock buttons without animation
      for (int i = AppConstants.FREE_GAMES_COUNT; i < menuGrid.buttons.length; i++) {
        menuGrid.buttons[i].isLocked = false;
      }
    }
  }

  /// ADDED: Start confetti celebration
  void _startConfettiCelebration() {
    try {
      if (_confettiComponent == null) {
        _confettiComponent = FlameConfetti(
          maxParticles: 150,
          autoPlay: false,
        );
      }

      // Add confetti component if not already added
      if (!children.contains(_confettiComponent!)) {
        add(_confettiComponent!);
      }

      // Create multiple confetti bursts across the screen
      final gameSize = size;

      // Center burst
      _confettiComponent!.burst(
          Vector2(gameSize.x / 2, gameSize.y / 2), particleCount: 100);

      // Side bursts with delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (isMounted && _confettiComponent != null) {
          _confettiComponent!.burst(
              Vector2(gameSize.x * 0.2, gameSize.y * 0.3), particleCount: 80);
        }
      });

      Future.delayed(const Duration(milliseconds: 1200), () {
        if (isMounted && _confettiComponent != null) {
          _confettiComponent!.burst(
              Vector2(gameSize.x * 0.8, gameSize.y * 0.3), particleCount: 80);
        }
      });

      // Clean up confetti after celebration
      Future.delayed(const Duration(seconds: 10), () {
        if (isMounted && _confettiComponent != null &&
            children.contains(_confettiComponent!)) {
          remove(_confettiComponent!);
        }
      });

      if (kDebugMode) {
        print('🎊 Confetti celebration started');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error starting confetti: $e');
      }
    }
  }

  /// ADDED: Animate unlock of games with wave effect
  void _animateUnlockGames() {
    try {
      if (kDebugMode) {
        print('🔓 Starting unlock animation for locked games');
      }

      // Get locked game indices
      final lockedGameIndices = <int>[];
      for (int i = AppConstants.FREE_GAMES_COUNT; i < AppConstants.TOTAL_GAMES_COUNT; i++) {
        lockedGameIndices.add(i);
      }

      if (kDebugMode) {
        print('🔒 Found ${lockedGameIndices.length} games to unlock: $lockedGameIndices');
      }

      // Animate unlock with wave effect (left to right, top to bottom)
      for (int i = 0; i < lockedGameIndices.length; i++) {
        final gameIndex = lockedGameIndices[i];
        final delay = i * 200; // 200ms between each unlock

        Future.delayed(Duration(milliseconds: delay), () {
          if (isMounted && gameIndex < menuGrid.buttons.length) {
            try {
              final button = menuGrid.buttons[gameIndex];

              if (kDebugMode) {
                print('🔓 Unlocking game $gameIndex with animation');
              }

              // Play unlock sound
              AudioService.instance.playSound('match_sound.wav');

              // Unlock the button with animation
              button.unlockWithAnimation();
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error unlocking button $gameIndex: $e');
              }
            }
          }
        });
      }

      // After all animations complete, DO NOT refresh again - buttons are already unlocked
      Future.delayed(Duration(milliseconds: lockedGameIndices.length * 200 + 2000), () {
        if (kDebugMode) {
          print('✅ All unlock animations completed');
        }
        // DO NOT call refreshButtonStates here - it causes double refresh
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in animate unlock games: $e');
      }
      // Fallback - just unlock buttons without refresh
      for (int i = AppConstants.FREE_GAMES_COUNT; i < menuGrid.buttons.length; i++) {
        if (i < menuGrid.buttons.length) {
          menuGrid.buttons[i].isLocked = false;
        }
      }
    }
  }

  /// ADDED: Force menu layout refresh (called from HomeScreen)
  void refreshMenuLayout() {
    if (isMounted && children.contains(menuGrid)) {
      menuGrid.forceRelayout();
      if (kDebugMode) {
        print('🔄 Forced menu layout refresh');
      }
    }
  }

  /// Get purchase manager instance for external use
  PurchaseManager get purchaseManager => _purchaseManager;

  /// Check purchase status (for compatibility)
  bool get isFullGamePurchased => _purchaseManager.isPurchased;

  @override
  void onRemove() {
    _clearPurchaseOverlays();
    super.onRemove();
  }
}