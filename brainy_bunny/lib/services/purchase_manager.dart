// lib/services/purchase_manager.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../constants/app_constants.dart';

/// Purchase manager for kids games with enhanced price debugging
class PurchaseManager {
  // Singleton pattern
  static final PurchaseManager _instance = PurchaseManager._internal();
  factory PurchaseManager() => _instance;
  PurchaseManager._internal();

  // Configuration
  static String get _productId => AppConstants.PRODUCT_ID;
  static const String _appSalt = AppConstants.APP_SALT;
  static const int _freeGamesCount = AppConstants.FREE_GAMES_COUNT;

  // State
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  bool _isAvailable = false;
  bool _isPurchased = false;
  bool _isInitialized = false;
  String? _deviceId;

  // ENHANCED: Price management with detailed debugging
  ProductDetails? _productDetails;
  String? _realPrice;
  bool _priceLoaded = false;
  String? _priceLoadError;

  // FIXED: Make subscriptions nullable to prevent LateInitializationError
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Stream for purchase status change notifications
  final _purchaseStateController = StreamController<bool>.broadcast();
  Stream<bool> get purchaseStream => _purchaseStateController.stream;

  // ENHANCED: Stream for price updates with error info
  final _priceController = StreamController<String>.broadcast();
  Stream<String> get priceStream => _priceController.stream;

  // Callback functions for UI
  VoidCallback? onPurchaseSuccess;
  VoidCallback? onPurchaseError;
  VoidCallback? onPurchasePending;
  VoidCallback? onPurchaseInvalid;
  VoidCallback? onPurchasePendingRemove;

  // Getters
  bool get isPurchased => _isPurchased;
  bool get isInitialized => _isInitialized;
  bool get isFullGamePurchased => _isPurchased;
  bool get priceLoaded => _priceLoaded;
  String? get priceLoadError => _priceLoadError;

  // ENHANCED: Price getters with debug info
  String get displayPrice => _realPrice ?? AppConstants.FALLBACK_PRICE;
  ProductDetails? get productDetails => _productDetails;
  bool get isUsingFallbackPrice => _realPrice == AppConstants.FALLBACK_PRICE || _realPrice == null;

  /// ENHANCED initialization method with extensive debugging
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔧 === PURCHASE MANAGER INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   Is Web: $kIsWeb');
        print('   Debug mode: $kDebugMode');
        print('   Release mode: ${kReleaseMode}');
        print('   Product ID: $_productId');
        print('   Apple Review Environment: ${_isAppleReviewEnvironment()}');
      }

      // Check purchase service availability
      _isAvailable = await _inAppPurchase.isAvailable();

      if (kDebugMode) {
        print('   IAP Available: $_isAvailable');
        if (!_isAvailable) {
          print('   ⚠️ IAP not available - this could be due to:');
          print('     - Emulator without Google Play Services');
          print('     - App not uploaded to Play Console');
          print('     - Testing account not configured');
          print('     - Device/region restrictions');
        }
      }

      // Generate or get device ID
      _deviceId = await _getOrCreateDeviceId();

      // Ensure anonymous authentication
      await _ensureAnonymousAuth();

      // Load purchase status
      await _loadPurchaseStatusEnhanced();

      // Setup listeners and load product details
      if (_isAvailable) {
        _setupPurchaseListener();

        // CRITICAL: Load product details with enhanced debugging
        await _loadProductDetailsEnhanced();

        // Check for existing purchases
        await _checkExistingPurchasesEnhanced();

        if (kDebugMode) {
          print('   Purchase listener setup complete');
        }
      } else {
        // Set fallback price when IAP is not available
        _realPrice = AppConstants.FALLBACK_PRICE;
        _priceLoaded = true;
        _priceLoadError = 'IAP service not available';
        _priceController.add(_realPrice!);

        if (kDebugMode) {
          print('   Using fallback price due to IAP unavailable');
        }
      }

      _setupConnectivityListener();
      _isInitialized = true;

      // Force false on web platform for testing
      if (kIsWeb) {
        _isPurchased = false;
        if (kDebugMode) {
          print('   Web platform detected - forcing purchase to false');
        }
      }

      // APPLE REVIEW: Ensure IAP is always available in sandbox
      if (!kIsWeb && !_isAvailable) {
        if (kDebugMode) {
          print('⚠️ IAP not available - Apple Review may fail');
          print('   This could block App Store Review process');
        }
      }

      if (kDebugMode) {
        print('✅ === INITIALIZATION COMPLETE ===');
        print('   Available: $_isAvailable');
        print('   Purchase status: $_isPurchased');
        print('   Price: $displayPrice (fallback: $isUsingFallbackPrice)');
        print('   Price error: $_priceLoadError');
        print('   Device ID: $_deviceId');
        print('==========================================');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ === INITIALIZATION ERROR ===');
        print('   Error: $e');
        print('   Stack trace: $stackTrace');
        print('   Setting fallback state');
      }

      _isInitialized = true;
      _isAvailable = false;
      _isPurchased = false;
      _realPrice = AppConstants.FALLBACK_PRICE;
      _priceLoaded = true;
      _priceLoadError = 'Initialization failed: $e';
      _priceController.add(_realPrice!);
    }
  }

  /// ENHANCED: Load product details with extensive debugging
  Future<void> _loadProductDetailsEnhanced() async {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('🏷️ Skipping product details - IAP not available');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🏷️ === LOADING PRODUCT DETAILS ===');
        print('   Product ID: $_productId');
        print('   Querying Google Play Store...');
      }

      // Add timeout to prevent hanging
      final response = await _inAppPurchase.queryProductDetails({_productId})
          .timeout(const Duration(seconds: 10));

      if (kDebugMode) {
        print('📦 === PRODUCT QUERY RESPONSE ===');
        print('   Products found: ${response.productDetails.length}');
        print('   Not found IDs: ${response.notFoundIDs}');
        print('   Error: ${response.error}');

        if (response.error != null) {
          print('   Error code: ${response.error!.code}');
          print('   Error message: ${response.error!.message}');
          print('   Error details: ${response.error!.details}');
        }
      }

      if (response.productDetails.isNotEmpty) {
        _productDetails = response.productDetails.first;
        _realPrice = _productDetails!.price;
        _priceLoaded = true;
        _priceLoadError = null;

        if (kDebugMode) {
          print('✅ === REAL PRICE LOADED ===');
          print('   Price: ${_productDetails!.price}');
          print('   Title: ${_productDetails!.title}');
          print('   Description: ${_productDetails!.description}');
          print('   Currency code: ${_productDetails!.currencyCode}');
          print('   Raw price: ${_productDetails!.rawPrice}');
          print('   Currency symbol: ${_productDetails!.currencySymbol}');

          // Additional debugging info
          if (_productDetails!.price == AppConstants.FALLBACK_PRICE) {
            print('   ⚠️ WARNING: Real price matches fallback - this might be a coincidence');
          }
        }

        _priceController.add(_realPrice!);

      } else {
        _priceLoadError = 'Product not found in store';

        if (kDebugMode) {
          print('❌ === NO PRODUCTS FOUND ===');
          print('   This could indicate:');
          print('   1. Product ID "$_productId" not configured in Play Console');
          print('   2. App not published (even to internal testing)');
          print('   3. Wrong package name/bundle ID');
          print('   4. Product not active in Play Console');
          print('   5. Testing account not added to internal testing');
          print('   6. App installed from wrong source (not Play Store)');

          if (response.notFoundIDs.isNotEmpty) {
            print('   Not found product IDs: ${response.notFoundIDs}');
          }
        }

        _realPrice = AppConstants.FALLBACK_PRICE;
        _priceLoaded = true;
        _priceController.add(_realPrice!);
      }

    } catch (e, stackTrace) {
      _priceLoadError = 'Failed to load price: $e';

      if (kDebugMode) {
        print('❌ === PRODUCT LOADING ERROR ===');
        print('   Error: $e');
        print('   Stack trace: $stackTrace');
        print('   This could be due to:');
        print('   - Network connectivity issues');
        print('   - Google Play Store not available');
        print('   - App not properly configured in Play Console');
        print('   - Device/account restrictions');
      }

      _realPrice = AppConstants.FALLBACK_PRICE;
      _priceLoaded = true;
      _priceController.add(_realPrice!);
    }
  }

  /// ENHANCED: Method to refresh price with detailed retry logic
  Future<void> refreshPrice({bool forceRefresh = false}) async {
    if (kDebugMode) {
      print('🔄 === REFRESHING PRICE ===');
      print('   Force refresh: $forceRefresh');
      print('   Current price: $_realPrice');
      print('   IAP available: $_isAvailable');
    }

    if (!_isAvailable && !forceRefresh) {
      _realPrice = AppConstants.FALLBACK_PRICE;
      _priceLoaded = true;
      _priceLoadError = 'IAP service not available';
      _priceController.add(_realPrice!);
      return;
    }

    _priceLoaded = false;
    _priceLoadError = null;

    // Re-check IAP availability if force refresh
    if (forceRefresh) {
      _isAvailable = await _inAppPurchase.isAvailable();
      if (kDebugMode) {
        print('   Re-checked IAP availability: $_isAvailable');
      }
    }

    await _loadProductDetailsEnhanced();
  }

  /// ENHANCED: Debug method to print current state
  void printDebugInfo() {
    if (kDebugMode) {
      print('🔍 === PURCHASE MANAGER STATE ===');
      print('   Initialized: $_isInitialized');
      print('   IAP Available: $_isAvailable');
      print('   Purchased: $_isPurchased');
      print('   Price loaded: $_priceLoaded');
      print('   Display price: $displayPrice');
      print('   Using fallback: $isUsingFallbackPrice');
      print('   Price error: $_priceLoadError');
      print('   Product details: ${_productDetails != null ? 'Available' : 'Not available'}');
      if (_productDetails != null) {
        print('   Product ID: ${_productDetails!.id}');
        print('   Product title: ${_productDetails!.title}');
        print('   Product price: ${_productDetails!.price}');
        print('   Product currency: ${_productDetails!.currencyCode}');
      }
      print('================================');
    }
  }

  /// ENHANCED: Load purchase status with Firebase priority for Release builds
  Future<void> _loadPurchaseStatusEnhanced() async {
    try {
      bool localSecureStatus = false;
      bool localPrefsStatus = false;
      bool firebaseStatus = false;

      // 1. Check local storage
      localSecureStatus = await _checkSecureStorage();
      localPrefsStatus = await _checkSharedPreferences();

      // 2. Check Firebase first if we have internet
      if (await _hasInternetConnection()) {
        try {
          firebaseStatus = await _checkFirebasePurchase();
          if (kDebugMode) {
            print('🔥 Firebase purchase check: $firebaseStatus');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Firebase check failed: $e');
          }
        }
      }

      // 3. Enhanced decision logic with Firebase priority
      if (firebaseStatus) {
        _isPurchased = true;
        if (!localSecureStatus || !localPrefsStatus) {
          await _saveLocalPurchase(true, null);
        }
        if (kDebugMode) {
          print('✅ Purchase status from Firebase: true');
        }
      } else if (localSecureStatus || localPrefsStatus) {
        _isPurchased = true;
        if (kDebugMode) {
          print('✅ Purchase status from local storage: true');
        }
      } else {
        _isPurchased = false;
        if (kDebugMode) {
          print('❌ No purchase found in any source');
        }
      }

      if (kDebugMode) {
        print('📊 Purchase status summary:');
        print('   - Secure storage: $localSecureStatus');
        print('   - SharedPrefs: $localPrefsStatus');
        print('   - Firebase: $firebaseStatus');
        print('   - Final decision: $_isPurchased');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading enhanced purchase status: $e');
      }
      _isPurchased = false;
    }
  }

  /// OPTIMIZED: Check for existing purchases without blocking delays
  Future<void> _checkExistingPurchasesEnhanced() async {
    if (!_isAvailable) return;

    try {
      if (kDebugMode) {
        print('🔍 Checking for existing purchases (optimized)...');
      }

      // Start restore purchases in background - don't wait for it
      _inAppPurchase.restorePurchases().then((_) {
        if (kDebugMode) {
          print('✅ Restore purchases completed in background');
        }
      }).catchError((e) {
        if (kDebugMode) {
          print('⚠️ Restore purchases failed: $e');
        }
      });

      // Check Firebase immediately if we have internet (don't wait for restore)
      if (!_isPurchased && await _hasInternetConnection()) {
        try {
          final firebaseStatus = await _checkFirebasePurchase();
          if (firebaseStatus) {
            if (kDebugMode) {
              print('🔥 Firebase override: Setting purchase to true based on Firebase record');
            }
            _isPurchased = true;
            await _saveLocalPurchase(true, null);
            _purchaseStateController.add(true);
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Firebase check failed: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Optimized existing purchase check completed');
        print('   Final purchase status: $_isPurchased');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in optimized existing purchase check: $e');
      }
    }
  }

  Future<bool> showParentGate(BuildContext context) async {
    final random = Random();

    // Generate two-digit addition problem
    final a = 15 + random.nextInt(75); // 15-89
    final b = 10 + random.nextInt(30); // 10-39
    final correctAnswer = a + b;

    // Avoid too simple problems
    if (a % 10 == 0 && b % 10 == 0) {
      return showParentGate(context); // Regenerate problem
    }

    String? userAnswer;
    bool isCorrect = false;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          'Adult Verification',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'This feature is for adults only.\nPlease solve the problem:',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$a + $b = ?',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
            const SizedBox(height: 20),
            TextField(
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                hintText: 'Enter answer',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.all(16),
              ),
              style: const TextStyle(fontSize: 18),
              autofocus: true,
              onChanged: (value) => userAnswer = value,
              onSubmitted: (value) {
                if (value == correctAnswer.toString()) {
                  isCorrect = true;
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () {
              if (userAnswer == correctAnswer.toString()) {
                isCorrect = true;
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Incorrect answer. Please try again.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Verify', style: TextStyle(fontSize: 16)),
          ),
        ],
      ),
    );

    return isCorrect;
  }

  /// Start purchase process with enhanced debugging
  Future<void> purchaseFullGame() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (kDebugMode) {
      print('🛒 === STARTING PURCHASE ===');
      printDebugInfo();
    }

    if (!_isAvailable) {
      if (kDebugMode) {
        print('❌ IAP Service not available for purchase');
      }
      _callOnPurchaseError();
      return;
    }

    try {
      // Use already loaded product details or reload if necessary
      if (_productDetails == null) {
        if (kDebugMode) {
          print('🔄 Product details not loaded, attempting to load...');
        }
        await _loadProductDetailsEnhanced();
      }

      if (_productDetails == null) {
        if (kDebugMode) {
          print('❌ Product details still not available after reload');
        }
        _callOnPurchaseError();
        return;
      }

      // Start purchase with loaded product details
      if (kDebugMode) {
        print('✅ Starting purchase:');
        print('   Product: ${_productDetails!.title}');
        print('   Price: ${_productDetails!.price}');
        print('   Currency: ${_productDetails!.currencyCode}');
      }

      final purchaseParam = PurchaseParam(productDetails: _productDetails!);
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Exception during purchase: $e');
        print('   Stack trace: $stackTrace');
      }
      _callOnPurchaseError();
    }
  }

  /// Check if game/level is unlocked
  bool isGameUnlocked(int gameIndex) {
    // Free games are always available
    if (gameIndex < _freeGamesCount) {
      return true;
    }

    // Others require purchase
    return _isPurchased;
  }

  /// Restore purchases - wrapper around InAppPurchase.restorePurchases()
  Future<void> restorePurchases() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_isAvailable) {
      if (kDebugMode) {
        print('❌ Cannot restore purchases - IAP service not available');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🔄 Starting purchase restoration...');
      }

      await _inAppPurchase.restorePurchases();

      if (kDebugMode) {
        print('✅ Purchase restoration completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during purchase restoration: $e');
      }
    }
  }

  /// FIXED: Safe disposal that handles nullable subscriptions
  void dispose() {
    try {
      _subscription?.cancel();
      _connectivitySubscription?.cancel();
      _subscription = null;
      _connectivitySubscription = null;

      if (!_purchaseStateController.isClosed) {
        _purchaseStateController.close();
      }

      if (!_priceController.isClosed) {
        _priceController.close();
      }

      if (kDebugMode) {
        print('PurchaseManager disposed safely');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing PurchaseManager: $e');
      }
    }
  }

  // ========== PRIVATE METHODS ==========

  /// Setup purchase listener
  void _setupPurchaseListener() {
    final purchaseStream = _inAppPurchase.purchaseStream;
    _subscription = purchaseStream.listen(
      _handlePurchaseUpdates,
      onError: (error) {
        if (kDebugMode) {
          print('Purchase stream error: $error');
        }
      },
    );
  }

  /// Handle purchase updates - ENHANCED to handle "already owned" scenario
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) async {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.productID == _productId) {

        if (kDebugMode) {
          print('🔄 Purchase update: ${purchaseDetails.status}');
          print('   Product ID: ${purchaseDetails.productID}');
          print('   Purchase ID: ${purchaseDetails.purchaseID}');
        }

        if (purchaseDetails.status == PurchaseStatus.pending) {
          if (kDebugMode) {
            print('⏳ Purchase pending...');
          }
          _callOnPurchasePending();
        } else {
          if (kDebugMode) {
            print('🧹 Removing pending overlay - final status received');
          }
          _callOnPurchasePendingRemove();

          if (purchaseDetails.status == PurchaseStatus.error) {
            if (kDebugMode) {
              print('❌ Purchase failed: ${purchaseDetails.error}');

              // Check if this is an "already owned" error
              if (purchaseDetails.error?.code == 'already_owned') {
                print('🔄 Item already owned - treating as successful purchase');
                await _handleSuccessfulPurchase(purchaseDetails);
                return;
              }
            }
            _handlePurchaseError(purchaseDetails.error!);
          } else if (purchaseDetails.status == PurchaseStatus.purchased ||
              purchaseDetails.status == PurchaseStatus.restored) {
            if (kDebugMode) {
              print('✅ Purchase successful/restored!');
            }
            await _handleSuccessfulPurchase(purchaseDetails);
          }
        }

        // Complete transaction
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
          if (kDebugMode) {
            print('✅ Purchase completed');
          }
        }
      }
    }
  }

  /// Handle successful purchase
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (kDebugMode) {
        print('🎉 Processing successful purchase...');
      }

      // Update local status
      _isPurchased = true;
      await _saveLocalPurchase(true, purchaseDetails);

      // Save to Firebase if online
      if (await _hasInternetConnection()) {
        await _savePurchaseToFirebase(purchaseDetails);
      }

      // Notify listeners
      _purchaseStateController.add(true);

      await Future.delayed(const Duration(milliseconds: 500));

      _callOnPurchaseSuccess();

      if (kDebugMode) {
        print('✅ Purchase successful and saved');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling successful purchase: $e');
      }
      _callOnPurchaseError();
    }
  }

  /// Handle purchase error - ENHANCED to handle "already owned"
  void _handlePurchaseError(IAPError error) {
    if (kDebugMode) {
      print('Purchase error: ${error.code} - ${error.message}');
    }

    // Check for "already owned" error and treat it as success
    if (error.code == 'already_owned') {
      if (kDebugMode) {
        print('🔄 Already owned error - marking as purchased');
      }

      _isPurchased = true;
      _saveLocalPurchase(true, null);
      _purchaseStateController.add(true);
      _callOnPurchaseSuccess();
      return;
    }

    _callOnPurchaseError();
  }

  /// Check secure storage
  Future<bool> _checkSecureStorage() async {
    try {
      const secureStorage = FlutterSecureStorage();
      final purchaseStatus = await secureStorage.read(key: 'brainy_bunny_purchase');
      final purchaseHash = await secureStorage.read(key: 'brainy_bunny_hash');

      if (purchaseStatus == 'purchased' && purchaseHash != null) {
        // Verify hash
        final expectedHash = _generatePurchaseHash('purchased');
        return purchaseHash == expectedHash;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Check SharedPreferences
  Future<bool> _checkSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('brainy_bunny_full_game_purchased') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Save purchase locally
  Future<void> _saveLocalPurchase(bool isPurchased, PurchaseDetails? details) async {
    try {
      // Save to secure storage
      const secureStorage = FlutterSecureStorage();
      if (isPurchased) {
        await secureStorage.write(key: 'brainy_bunny_purchase', value: 'purchased');
        await secureStorage.write(key: 'brainy_bunny_hash', value: _generatePurchaseHash('purchased'));
        if (details != null) {
          await secureStorage.write(key: 'brainy_bunny_purchase_id', value: details.purchaseID ?? '');
          await secureStorage.write(key: 'brainy_bunny_purchase_date', value: DateTime.now().toIso8601String());
        }
      } else {
        await secureStorage.delete(key: 'brainy_bunny_purchase');
        await secureStorage.delete(key: 'brainy_bunny_hash');
        await secureStorage.delete(key: 'brainy_bunny_purchase_id');
        await secureStorage.delete(key: 'brainy_bunny_purchase_date');
      }

      // Duplicate in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('brainy_bunny_full_game_purchased', isPurchased);
      if (isPurchased && details != null) {
        await prefs.setString('brainy_bunny_last_purchase_id', details.purchaseID ?? '');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error saving purchase locally: $e');
      }
    }
  }

  /// Save purchase to Firebase
  Future<void> _savePurchaseToFirebase(PurchaseDetails purchaseDetails) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final purchaseData = {
        'product_id': _productId,
        'purchase_id': purchaseDetails.purchaseID,
        'purchase_date': FieldValue.serverTimestamp(),
        'platform': Platform.isIOS ? 'ios' : 'android',
        'device_id': _deviceId,
        'app_version': '1.0.0',
        'verification_data': purchaseDetails.verificationData.serverVerificationData,
        'build_mode': kReleaseMode ? 'release' : 'debug',
      };

      await FirebaseFirestore.instance
          .collection('brainy_bunny_purchases')
          .doc(user.uid)
          .set(purchaseData, SetOptions(merge: true));

      if (_deviceId != null) {
        await FirebaseFirestore.instance
            .collection('brainy_bunny_device_purchases')
            .doc(_deviceId)
            .set(purchaseData, SetOptions(merge: true));
      }

      if (kDebugMode) {
        print('Purchase saved to Firebase');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error saving purchase to Firebase: $e');
      }
    }
  }

  /// Check purchase in Firebase
  Future<bool> _checkFirebasePurchase() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final userPurchaseDoc = await FirebaseFirestore.instance
          .collection('brainy_bunny_purchases')
          .doc(user.uid)
          .get();

      if (userPurchaseDoc.exists) {
        final data = userPurchaseDoc.data();
        if (data != null && data['product_id'] == _productId) {
          if (kDebugMode) {
            print('🔥 Found purchase in Firebase by UID');
          }
          return true;
        }
      }

      if (_deviceId != null) {
        final devicePurchaseDoc = await FirebaseFirestore.instance
            .collection('brainy_bunny_device_purchases')
            .doc(_deviceId)
            .get();

        if (devicePurchaseDoc.exists) {
          final data = devicePurchaseDoc.data();
          if (data != null && data['product_id'] == _productId) {
            if (kDebugMode) {
              print('🔥 Found purchase in Firebase by device ID');
            }
            await FirebaseFirestore.instance
                .collection('brainy_bunny_purchases')
                .doc(user.uid)
                .set(data, SetOptions(merge: true));
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking Firebase purchase: $e');
      }
      return false;
    }
  }

  /// Generate device ID
  Future<String> _getOrCreateDeviceId() async {
    try {
      const secureStorage = FlutterSecureStorage();
      String? deviceId = await secureStorage.read(key: 'brainy_bunny_device_id');

      if (deviceId == null) {
        deviceId = const Uuid().v4();
        await secureStorage.write(key: 'brainy_bunny_device_id', value: deviceId);

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('brainy_bunny_device_id', deviceId);
      }

      return deviceId;
    } catch (e) {
      try {
        final prefs = await SharedPreferences.getInstance();
        String? deviceId = prefs.getString('brainy_bunny_device_id');
        if (deviceId == null) {
          deviceId = const Uuid().v4();
          await prefs.setString('brainy_bunny_device_id', deviceId);
        }
        return deviceId;
      } catch (e2) {
        return _generateFallbackDeviceId();
      }
    }
  }

  /// Generate fallback device ID
  Future<String> _generateFallbackDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String identifier = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        identifier = '${androidInfo.brand}_${androidInfo.model}_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        identifier = '${iosInfo.name}_${iosInfo.model}_${iosInfo.identifierForVendor}';
      } else {
        identifier = 'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
      }

      final bytes = utf8.encode(identifier + _appSalt);
      final digest = sha256.convert(bytes);
      return digest.toString().substring(0, 32);

    } catch (e) {
      return const Uuid().v4();
    }
  }

  /// Ensure anonymous authentication
  Future<void> _ensureAnonymousAuth() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        await FirebaseAuth.instance.signInAnonymously();
        if (kDebugMode) {
          print('Anonymous authentication successful');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Anonymous authentication failed: $e');
      }
    }
  }

  /// Generate purchase hash
  String _generatePurchaseHash(String data) {
    final combinedData = '$data:$_productId:$_appSalt:$_deviceId';
    final bytes = utf8.encode(combinedData);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Check if running in Apple Review environment
  bool _isAppleReviewEnvironment() {
    if (!Platform.isIOS) return false;

    // Apple Review typically runs in sandbox with specific characteristics
    // This is a heuristic check - not 100% reliable but helps with debugging
    return !kDebugMode && // Not debug mode
           !kReleaseMode; // Not release mode (TestFlight/Review builds)
  }

  /// Check internet connection
  Future<bool> _hasInternetConnection() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      return !connectivityResults.contains(ConnectivityResult.none);
    } catch (e) {
      return false;
    }
  }

  /// Setup connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
          (List<ConnectivityResult> results) {
        if (!results.contains(ConnectivityResult.none)) {
          _syncWithFirebase();
        }
      },
    );
  }

  /// Sync with Firebase
  Future<void> _syncWithFirebase() async {
    try {
      if (!await _hasInternetConnection()) return;

      final localPurchase = await _checkSecureStorage() || await _checkSharedPreferences();
      final firebasePurchase = await _checkFirebasePurchase();

      if (!localPurchase && firebasePurchase) {
        _isPurchased = true;
        await _saveLocalPurchase(true, null);
        _purchaseStateController.add(true);

        if (kDebugMode) {
          print('🔄 Synced purchase from Firebase to local storage');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error syncing with Firebase: $e');
      }
    }
  }

  // Helper methods to call callback functions
  void _callOnPurchaseSuccess() {
    onPurchaseSuccess?.call();
  }

  void _callOnPurchaseError() {
    onPurchaseError?.call();
  }

  void _callOnPurchasePending() {
    onPurchasePending?.call();
  }

  void _callOnPurchaseInvalid() {
    onPurchaseInvalid?.call();
  }

  void _callOnPurchasePendingRemove() {
    onPurchasePendingRemove?.call();
  }
}